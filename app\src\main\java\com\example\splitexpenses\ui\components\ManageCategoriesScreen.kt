package com.example.splitexpenses.ui.components

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.splitexpenses.data.Category
import com.example.splitexpenses.ui.viewmodels.CategoriesViewModel
import org.json.JSONArray

@Composable
fun loadEmojiListFromAssets(): List<String> {
    val context = LocalContext.current
    return remember {
        try {
            val jsonString = context.assets.open("emoji.json")
                .bufferedReader().use { it.readText() }

            val jsonArray = JSONArray(jsonString)

            // Parse and extract the "emoji" field
            val emojis = mutableListOf<String>()
            for (i in 0 until jsonArray.length()) {
                val emoji = jsonArray.getJSONObject(i).optString("emoji", "")
                if (emoji.isNotBlank()) {
                    emojis.add(emoji)
                }
            }
            emojis
        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
}

@Composable
fun EmojiPickerFromJson(
    onEmojiSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val emojis = loadEmojiListFromAssets()

    LazyVerticalGrid(
        columns = GridCells.Fixed(9),
        modifier = modifier
            .fillMaxWidth()
            .height(300.dp)
            .padding(8.dp)
    ) {
        items(emojis) { emoji ->
            Text(
                text = emoji,
                fontSize = 20.sp,
                modifier = Modifier
                    .padding(6.dp)
                    .clickable { onEmojiSelected(emoji) },
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun ManageCategoriesScreen(
    onBackClick: () -> Unit,
    viewModel: CategoriesViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    // Handle back navigation with save
    val handleBackWithSave = {
        viewModel.saveCategories()
        onBackClick()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = handleBackWithSave) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Manage Categories",
                style = MaterialTheme.typography.headlineLarge,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(48.dp))
        }

        Spacer(modifier = Modifier.height(16.dp))
        // Add new category section
        Surface(
            modifier = Modifier.fillMaxWidth(),
            shape = MaterialTheme.shapes.medium,
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.secondaryContainer),
            color = MaterialTheme.colorScheme.surface
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "Add New Category",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // Emoji and Category Name on the same row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Square emoji picker button
                    Surface(
                        modifier = Modifier
                            .size(width = 56.dp, height = 64.dp)
                            .clickable { viewModel.toggleNewEmojiPicker() },
                        shape = MaterialTheme.shapes.small,
                        border = BorderStroke(1.dp, MaterialTheme.colorScheme.secondaryContainer),
                        color = Color.Transparent
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = if (uiState.newCategoryEmoji.isNotBlank()) uiState.newCategoryEmoji else "😀",
                                style = MaterialTheme.typography.titleMedium,
                                textAlign = TextAlign.Center
                            )
                        }
                    }

                    // Category name field takes remaining space
                    OutlinedTextField(
                        value = uiState.newCategoryName,
                        onValueChange = { viewModel.updateNewCategoryName(it) },
                        label = { Text("Category Name") },
                        modifier = Modifier.weight(1f),
                        singleLine = true,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    )
                }

                // Show emoji picker for new category
                if (uiState.showNewEmojiPicker) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Surface(
                        modifier = Modifier.fillMaxWidth(),
                        shape = MaterialTheme.shapes.small,
                        border = BorderStroke(1.dp, MaterialTheme.colorScheme.secondaryContainer),
                        color = MaterialTheme.colorScheme.surface
                    ) {
                        EmojiPickerFromJson(
                            onEmojiSelected = { emoji ->
                                viewModel.updateNewCategoryEmoji(emoji)
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Keywords section
                Text(
                    text = "Keywords (Optional)",
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(bottom = 4.dp)
                )

                // Keyword input field with add button
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = uiState.newKeywordInput,
                        onValueChange = { viewModel.updateNewKeywordInput(it) },
                        label = { Text("Add keyword") },
                        modifier = Modifier.weight(1f),
                        singleLine = true,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        ),
                        keyboardActions = KeyboardActions(
                            onDone = {
                                viewModel.addNewKeyword()
                            }
                        )
                    )

                    IconButton(
                        onClick = { viewModel.addNewKeyword() },
                        enabled = uiState.newKeywordInput.isNotBlank()
                    ) {
                        Icon(
                            Icons.Default.Add,
                            contentDescription = "Add keyword",
                            tint = if (uiState.newKeywordInput.isNotBlank()) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                }

                Column(
                    modifier = Modifier.animateContentSize()
                ) {
                    // Display current keywords as chips
                    if (uiState.newCategoryKeywords.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            contentPadding = PaddingValues(vertical = 4.dp)
                        ) {
                            items(uiState.newCategoryKeywords) { keyword ->
                                FilterChip(
                                    selected = false,
                                    onClick = { viewModel.removeNewKeyword(keyword) },
                                    label = {
                                        Text(
                                            text = keyword,
                                            color = MaterialTheme.colorScheme.secondary
                                        )
                                    },

                                    colors = FilterChipDefaults.filterChipColors(
                                        containerColor = MaterialTheme.colorScheme.secondaryContainer
                                    ),
                                    border = FilterChipDefaults.filterChipBorder(
                                        selected = true,
                                        enabled = true,
                                        borderColor = MaterialTheme.colorScheme.secondaryContainer
                                    )
                                )
                            }
                        }
                    }
                }

                // Show automatic keyword info
                if (uiState.newCategoryName.isNotBlank()) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Note: '${uiState.newCategoryName.lowercase()}' will be automatically added as a keyword",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontStyle = FontStyle.Italic
                    )
                }


                if (uiState.showError) {
                    Text(
                        text = "Category name and emoji are required",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }


                Spacer(modifier = Modifier.height(8.dp))

                Button(
                    onClick = {
                        viewModel.addCategory()
                        viewModel.saveCategories()
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Add Category")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Categories list
        Text(
            text = "Current Categories",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            items(uiState.categories) { category ->
                val isDefaultCategory = category.name == "None"
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    shape = MaterialTheme.shapes.small,
                    color = if (isDefaultCategory) {
                        MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
                    } else {
                        MaterialTheme.colorScheme.surface
                    },
                    border = BorderStroke(
                        width = if (isDefaultCategory) 2.dp else 1.dp,
                        color = if (isDefaultCategory) {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                        } else {
                            MaterialTheme.colorScheme.secondaryContainer
                        }
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp)
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = category.emoji,
                                    style = MaterialTheme.typography.titleMedium
                                )
                                Text(
                                    text = category.name,
                                    style = MaterialTheme.typography.titleMedium
                                )

                                // Show "Default" badge for None category
                                if (category.name == "None") {
                                    Surface(
                                        modifier = Modifier.padding(start = 8.dp),
                                        shape = MaterialTheme.shapes.small,
                                        color = MaterialTheme.colorScheme.primaryContainer
                                    ) {
                                        Text(
                                            text = "Default",
                                            style = MaterialTheme.typography.labelSmall,
                                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                                            modifier = Modifier.padding(
                                                horizontal = 8.dp,
                                                vertical = 2.dp
                                            )
                                        )
                                    }
                                }
                            }

                            // Always show edit button, but only show delete for non-default categories
                            Row {
                                IconButton(
                                    onClick = {
                                        viewModel.startEditingCategory(category)
                                    }
                                ) {
                                    Icon(
                                        Icons.Default.Edit,
                                        contentDescription = "Edit category",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }

                                // Only show delete button for non-default categories
                                if (category.name != "None") {
                                    IconButton(
                                        onClick = {
                                            viewModel.deleteCategory(category)
                                        }
                                    ) {
                                        Icon(
                                            Icons.Default.Delete,
                                            contentDescription = "Remove category",
                                            tint = MaterialTheme.colorScheme.error
                                        )
                                    }
                                }
                            }
                        }

                        // Show keywords for the category
                        if (category.keywords.isNotEmpty()) {
                            Text(
                                text = "Keywords: ${category.keywords.joinToString(", ")}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(start = 32.dp, top = 4.dp)
                            )
                        }
                    }
                }
            }
        }

        // Edit category dialog
        if (uiState.editingCategory != null) {
            val isDefaultCategory = uiState.editingCategory?.name == "None"
            AlertDialog(
                onDismissRequest = { viewModel.cancelEditingCategory() },
                containerColor = MaterialTheme.colorScheme.surface,
                tonalElevation = 8.dp,
                title = {
                    Column {
                        Text("Edit Category")
                        if (isDefaultCategory) {
                            Text(
                                text = "Editing Default Category",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                },
                text = {
                    Column(
                        modifier = Modifier.animateContentSize()
                    ) {
                        // Show info note for default category
                        if (isDefaultCategory) {
                            Surface(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 12.dp),
                                shape = MaterialTheme.shapes.small,
                                color = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
                            ) {
                                Text(
                                    text = "This is the default category used when no other category matches. It cannot be deleted but can be customized.",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    modifier = Modifier.padding(8.dp)
                                )
                            }
                        }

                        // Emoji and Category Name on the same row
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Square emoji picker button
                            Surface(
                                modifier = Modifier
                                    .size(width = 56.dp, height = 64.dp)
                                    .padding(top = 8.dp)
                                    .clickable { viewModel.toggleEditEmojiPicker() },
                                shape = MaterialTheme.shapes.small,
                                border = BorderStroke(
                                    1.dp,
                                    MaterialTheme.colorScheme.secondaryContainer
                                ),
                                color = Color.Transparent
                            ) {
                                Box(
                                    modifier = Modifier.fillMaxSize(),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = if (uiState.editEmoji.isNotBlank()) uiState.editEmoji else "\uD83C\uDFF7\uFE0F",
                                        style = MaterialTheme.typography.titleMedium,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }

                            // Category name field takes remaining space
                            OutlinedTextField(
                                value = uiState.editName,
                                onValueChange = { viewModel.updateEditName(it) },
                                label = { Text("Category Name") },
                                modifier = Modifier.weight(1f),
                                singleLine = true,
                                colors = TextFieldDefaults.colors(
                                    unfocusedContainerColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                                )
                            )
                        }

                        // Show emoji picker for edit category
                        if (uiState.showEditEmojiPicker) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Surface(
                                modifier = Modifier.fillMaxWidth(),
                                shape = MaterialTheme.shapes.small,
                                border = BorderStroke(
                                    1.dp,
                                    MaterialTheme.colorScheme.secondaryContainer
                                ),
                                color = MaterialTheme.colorScheme.surface
                            ) {
                                EmojiPickerFromJson(
                                    onEmojiSelected = { emoji ->
                                        viewModel.updateEditEmoji(emoji)
                                    }
                                )
                            }
                        }


                        Spacer(modifier = Modifier.height(8.dp))

                        // Keywords section
                        Text(
                            text = "Keywords (Optional)",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )

                        // Keyword input field with add button
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            OutlinedTextField(
                                value = uiState.editKeywordInput,
                                onValueChange = { viewModel.updateEditKeywordInput(it) },
                                label = { Text("Add keyword") },
                                modifier = Modifier.weight(1f),
                                singleLine = true,
                                colors = TextFieldDefaults.colors(
                                    unfocusedContainerColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        viewModel.addEditKeyword()
                                    }
                                )
                            )

                            IconButton(
                                onClick = { viewModel.addEditKeyword() },
                                enabled = uiState.editKeywordInput.isNotBlank()
                            ) {
                                Icon(
                                    Icons.Default.Add,
                                    contentDescription = "Add keyword",
                                    tint = if (uiState.editKeywordInput.isNotBlank()) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                    }
                                )
                            }
                        }

                        // Display current keywords as chips
                        if (uiState.editKeywords.isNotEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                            LazyRow(
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                contentPadding = PaddingValues(vertical = 4.dp)
                            ) {
                                items(uiState.editKeywords) { keyword ->
                                    FilterChip(
                                        selected = false,
                                        onClick = { viewModel.removeEditKeyword(keyword) },
                                        label = { Text(keyword) },
                                        trailingIcon = {
                                            Icon(
                                                Icons.Default.Close,
                                                contentDescription = "Remove keyword",
                                                modifier = Modifier.size(16.dp)
                                            )
                                        },
                                        colors = FilterChipDefaults.filterChipColors(
                                            containerColor = MaterialTheme.colorScheme.secondaryContainer
                                        )
                                    )
                                }
                            }
                        }

                        // Show automatic keyword info

                        if (uiState.editName.isNotBlank()) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = "Note: '${uiState.editName.lowercase()}' will be automatically added as a keyword",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                fontStyle = FontStyle.Italic
                            )
                        }

                    }
                },
                confirmButton = {
                    Button(
                        onClick = {
                            viewModel.saveEditedCategory()
                        }
                    ) {
                        Text("Save")
                    }
                },
                dismissButton = {
                    TextButton(onClick = { viewModel.cancelEditingCategory() }) {
                        Text("Cancel")
                    }
                }
            )
        }
    }
}
