package com.example.splitexpenses.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a2\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u0007\u001a$\u0010\t\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u0007\u001a \u0010\n\u001a\u00020\u00012\b\u0010\u000b\u001a\u0004\u0018\u00010\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u00a8\u0006\u000f"}, d2 = {"BarChart", "", "data", "", "", "colors", "Landroidx/compose/ui/graphics/Color;", "categoryNames", "", "PieChart", "StatisticsScreen", "group", "Lcom/example/splitexpenses/data/GroupData;", "onBackClick", "Lkotlin/Function0;", "app_debug"})
public final class StatisticsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class, androidx.compose.animation.ExperimentalAnimationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void StatisticsScreen(@org.jetbrains.annotations.Nullable()
    com.example.splitexpenses.data.GroupData group, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackClick) {
    }
    
    /**
     * A basic pie chart component without animations.
     * This provides a clean starting point for implementing custom animations.
     */
    @androidx.compose.runtime.Composable()
    public static final void PieChart(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Double> data, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> colors) {
    }
    
    /**
     * A bar chart component with animations.
     */
    @androidx.compose.runtime.Composable()
    public static final void BarChart(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Double> data, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> colors, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> categoryNames) {
    }
}